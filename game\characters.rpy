## 角色定義檔案
## 隔牆的溫柔 - 角色與圖片定義

## 角色定義
define m = Character("美咲", color="#c8ffc8")
define p = Character("你", color="#c8c8ff")
define narrator = Character(None, kind=nvl)

## 居中文本角色定義
define centered = Character(None, what_style="centered_text", window_style="centered_window")

## 圖片定義 - 美咲立繪
## 基本表情
image misaki normal = "characters/misaki/misaki_normal_01.png"
image misaki happy = "characters/misaki/misaki_happy_01.png"
image misaki sad = "characters/misaki/misaki_sad_01.png"
image misaki shy = "characters/misaki/misaki_shy_01.png"
image misaki surprised = "characters/misaki/misaki_surprised_01.png"
image misaki embarrassed = "characters/misaki/misaki_embarrassed_01.png"
image misaki gentle = "characters/misaki/misaki_gentle_01.png"
image misaki thoughtful = "characters/misaki/misaki_thoughtful_01.png"
image misaki calm = "characters/misaki/misaki_calm_01.png"
image misaki grateful = "characters/misaki/misaki_grateful_01.png"
image misaki crying = "characters/misaki/misaki_crying_01.png"
image misaki nervous = "characters/misaki/misaki_nervous_01.png"
image misaki determined = "characters/misaki/misaki_determined_01.png"
image misaki lustful = "characters/misaki/misaki_lustful_01.png"
image misaki satisfied = "characters/misaki/misaki_satisfied_01.png"
image misaki crying_happy = "characters/misaki/misaki_crying_happy_01.png"
image misaki wedding_dress = "characters/misaki/misaki_wedding_01.png"
image misaki mature = "characters/misaki/misaki_mature_01.png"
image misaki understanding = "characters/misaki/misaki_understanding_01.png"
image misaki confident = "characters/misaki/misaki_confident_01.png"
image misaki smile = "characters/misaki/misaki_smile_01.png"

## 背景圖片定義
image bg_male_bedroom = "backgrounds/male_bedroom.png"
image bg_hallway = "backgrounds/hallway.png"
image bg_misaki_apartment = "backgrounds/misaki_apartment.png"
image bg_misaki_living_room = "backgrounds/misaki_living_room.png"
image bg_misaki_bedroom = "backgrounds/misaki_bedroom.png"
image bg_restaurant = "backgrounds/restaurant.png"
image bg_new_apartment = "backgrounds/new_apartment.png"
image bg_park = "backgrounds/park.png"

## CG場景圖片定義
image cg_embrace = "cg/daily/embrace_01.png"
image cg_intimate1 = "cg/intimate/intimate_01.png"
image cg_intimate2 = "cg/intimate/intimate_02.png"
image cg_climax = "cg/intimate/climax_01.png"
image cg_afterglow = "cg/intimate/afterglow_01.png"
image cg_gentle = "cg/intimate/gentle_01.png"
image cg_proposal = "cg/daily/proposal_01.png"
image cg_secret_love = "cg/daily/secret_love_01.png"
image cg_farewell = "cg/daily/farewell_01.png"

## 變數初始化
default affection = 0
default chapter = 1
default ending_route = ""

## 自定義轉場效果
define fade_slow = Fade(1.0, 1.0, 1.0)
define dissolve_slow = Dissolve(2.0)

## 音效定義（待添加）
# define audio.bgm_main = "audio/bgm/main_theme.ogg"
# define audio.bgm_romantic = "audio/bgm/romantic_theme.ogg"
# define audio.bgm_intimate = "audio/bgm/intimate_theme.ogg"
# define audio.se_door_knock = "audio/se/door_knock.ogg"
# define audio.se_door_open = "audio/se/door_open.ogg"
