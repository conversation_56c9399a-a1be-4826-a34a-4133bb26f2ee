## 遊戲基本設定檔案
## 隔牆的溫柔 - Tenderness Beyond the Wall

## 遊戲基本信息
define config.name = _("隔牆的溫柔")
define build.name = "Tenderness-Beyond-the-Wall"

## 版本信息
define config.version = "1.0"

## 遊戲描述
define gui.about = _p("""
{b}隔牆的溫柔{/b}

一個關於孤獨、慾望與溫柔的成人故事。
33歲的寡婦美咲與鄰居男主角，
因一次意外的深夜邂逅而展開的親密關係。

製作：紫暮工坊 Twilight Purple Studio
版本：v1.0
""")

## 遊戲標識符
define config.save_directory = "TendernessWall-1234567890"

## 圖標設定
# define config.window_icon = "gui/window_icon.png"  # 圖標文件不存在，暫時註釋

## 建置設定
define build.include_update = False

## 隱私設定
define config.has_autosave = True
define config.has_quicksave = True

## 音效設定
define config.has_sound = True
define config.has_music = True
define config.has_voice = True

## 成人內容設定
define config.developer = True
define config.console = True

## 螢幕解析度設定
define config.screen_width = 1920
define config.screen_height = 1080

## 字體設定
# define gui.text_font = "gui/font/NotoSansCJK-Regular.ttc"  # 字體文件不存在，使用預設字體
# define gui.name_text_font = "gui/font/NotoSansCJK-Medium.ttc"
# define gui.interface_text_font = "gui/font/NotoSansCJK-Regular.ttc"

## 語言設定
define config.language = "zh_TW"

## 存檔設定
define config.save_json_callbacks = []

## 快捷鍵設定
define config.keymap = {
    'game_menu': ['mouseup_3', 'K_ESCAPE'],
    'hide_windows': ['mouseup_2', 'h']
}

## 轉場效果設定
define config.enter_transition = dissolve
define config.exit_transition = dissolve
define config.intra_transition = dissolve
define config.after_load_transition = None

## 視窗設定
define config.window = "auto"
define config.window_show_transition = Dissolve(.2)
define config.window_hide_transition = Dissolve(.2)

## 預設文字速度
default preferences.text_cps = 30
default preferences.auto_forward_time = 15

## 音量設定
default preferences.music_volume = 0.7
default preferences.sound_volume = 0.8
default preferences.voice_volume = 0.9

## 全螢幕設定
default preferences.fullscreen = False

## 成人內容警告
define config.autosave_on_quit = True

## 建置排除檔案
define build.exclude_empty_directories = True

init python:
    ## 建置設定
    build.classify('**~', None)
    build.classify('**.bak', None)
    build.classify('**/.**', None)
    build.classify('**/#**', None)
    build.classify('**/thumbs.db', None)
    
    ## 文檔檔案
    build.classify('**.md', None)
    build.classify('**.txt', None)
    
    ## 開發檔案
    build.classify('memory-bank/**', None)
    build.classify('temp/**', None)
    build.classify('renpy-8.4.1-sdk/**', None)
    build.classify('**.zip', None)

## 成人內容年齡確認
define config.check_conflicting_properties = True
