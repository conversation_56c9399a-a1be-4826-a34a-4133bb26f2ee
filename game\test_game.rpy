## 遊戲測試文件
## 用於測試基本功能和劇情流程

## 測試開始標籤
label test_start:
    
    scene black
    with fade
    
    centered "{size=+15}{color=#ffc8c8}遊戲測試模式{/color}{/size}"
    
    pause 2.0
    
    menu:
        "測試基本對話":
            jump test_dialogue
        
        "測試角色立繪":
            jump test_characters
        
        "測試選擇系統":
            jump test_choices
        
        "開始正式遊戲":
            jump start

## 測試對話系統
label test_dialogue:
    
    scene bg_hallway
    with fade
    
    "這是旁白測試。"
    
    show misaki normal
    with dissolve
    
    m "這是美咲的對話測試。"
    
    p "這是玩家的對話測試。"
    
    "對話系統測試完成。"
    
    jump test_start

## 測試角色立繪
label test_characters:
    
    scene bg_misaki_living_room
    with fade
    
    "現在測試美咲的各種表情："
    
    show misaki normal
    with dissolve
    
    "正常表情"
    
    show misaki happy
    with dissolve
    
    "開心表情"
    
    show misaki sad
    with dissolve
    
    "悲傷表情"
    
    show misaki shy
    with dissolve
    
    "害羞表情"
    
    show misaki surprised
    with dissolve
    
    "驚訝表情"
    
    "角色立繪測試完成。"
    
    jump test_start

## 測試選擇系統
label test_choices:
    
    scene bg_misaki_apartment
    with fade
    
    show misaki normal
    with dissolve
    
    m "請選擇一個選項："
    
    menu:
        "選項A":
            $ affection += 1
            m "你選擇了A，好感度+1"
        
        "選項B":
            $ affection += 2
            m "你選擇了B，好感度+2"
        
        "選項C":
            $ affection += 3
            m "你選擇了C，好感度+3"
    
    m "當前好感度：[affection]"
    
    "選擇系統測試完成。"
    
    jump test_start
