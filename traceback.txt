﻿I'm sorry, but an uncaught exception occurred.

After initialization, but before game start.
FileNotFoundError: Couldn't find file 'gui/window_icon.png'.

-- Full Traceback ------------------------------------------------------------

Traceback (most recent call last):
  File "renpy/bootstrap.py", line 376, in bootstrap
    renpy.main.main()
    ~~~~~~~~~~~~~~~^^
  File "renpy/main.py", line 529, in main
    renpy.game.script.report_duplicate_labels()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "renpy/script.py", line 1222, in report_duplicate_labels
    if renpy.parser.report_parse_errors():
       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^ 
  File "renpy/parser.py", line 1794, in report_parse_errors
    renpy.display.error.report_parse_errors(screen_parse_errors, error_fn)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "renpy/display/error.py", line 181, in report_parse_errors
    init_display()
    ~~~~~~~~~~~~^^
  File "renpy/display/error.py", line 70, in init_display
    renpy.game.interface.start()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "renpy/display/core.py", line 1019, in start
    self.post_init()
    ~~~~~~~~~~~~~~^^
  File "renpy/display/core.py", line 1102, in post_init
    self.set_icon()
    ~~~~~~~~~~~~~^^
  File "renpy/display/core.py", line 1155, in set_icon
    with renpy.loader.load(icon, directory="images") as f:
         ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^      
  File "renpy/loader.py", line 698, in load
    raise FileNotFoundError("Couldn't find file '%s'." % name)
FileNotFoundError: Couldn't find file 'gui/window_icon.png'.

Windows-11-10.0.22631-SP0 AMD64
Ren'Py 8.4.1.25072401
隔牆的溫柔 1.0
Sun Jul 27 18:30:25 2025
