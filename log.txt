2025-07-27 10:26:01 UTC
Windows-11-10.0.22631-SP0
Ren'Py 8.4.1.25072401

Early init took 46 ms
Loading error handling took 26 ms
Loading script took 628 ms
Loading save slot metadata took 2 ms
Loading persistent took 0 ms

File "game/options.rpy", line 61: expected '=' not found.
    define config.keymap['game_menu'].append('mouseup_3')
                                     ^

File "game/options.rpy", line 62: expected '=' not found.
    define config.keymap['hide_windows'].append('mouseup_2')
                                        ^
DPI scale factor: 1.000000
nvdrs: Loaded, about to disable thread optimizations.
nvdrs: Disabled thread optimizations.
Interface start took 389 ms

Initializing gl2 renderer:
primary display bounds: (0, 0, 1920, 1080)
swap interval: 1 frames
Windowed mode.
Vendor: 'NVIDIA Corporation'
Renderer: 'NVIDIA GeForce RTX 3070 Laptop GPU/PCIe/SSE2'
Version: '4.6.0 NVIDIA 576.88'
Display Info: <Info({'bitsize': 32, 'bytesize': 4, 'masks': (16711680, 65280, 255, 0), 'shifts': (16, 8, 0, 0), 'losses': (0, 0, 0, 8), 'current_w': 1920, 'current_h': 1080, 'refresh_rate': 60, 'hw': False, 'wm': True, 'video_mem': 268435456, 'blit_hw': False, 'blit_hw_CC': False, 'blit_hw_A': False, 'blit_sw': False, 'blit_sw_CC': False, 'blit_sw_A': False})>
Screen sizes: virtual=(1280, 720) physical=(1280, 720) drawable=(1280, 720)
Maximum texture size: 4096x4096
Could not open 'cache/shaders.txt':
Resetting cache.
Early init took 4 ms
Loading error handling took 5 ms
Loading script took 90 ms
Loading save slot metadata took 2 ms
Loading persistent took 0 ms

File "game/options.rpy", line 61: expected '=' not found.
    define config.keymap['game_menu'].append('mouseup_3')
                                     ^

File "game/options.rpy", line 62: expected '=' not found.
    define config.keymap['hide_windows'].append('mouseup_2')
                                        ^
DPI scale factor: 1.000000
nvdrs: Loaded, about to disable thread optimizations.
nvdrs: Disabled thread optimizations.
Interface start took 267 ms

Initializing gl2 renderer:
primary display bounds: (0, 0, 1920, 1080)
swap interval: 1 frames
Windowed mode.
Vendor: 'NVIDIA Corporation'
Renderer: 'NVIDIA GeForce RTX 3070 Laptop GPU/PCIe/SSE2'
Version: '4.6.0 NVIDIA 576.88'
Display Info: <Info({'bitsize': 32, 'bytesize': 4, 'masks': (16711680, 65280, 255, 0), 'shifts': (16, 8, 0, 0), 'losses': (0, 0, 0, 8), 'current_w': 1920, 'current_h': 1080, 'refresh_rate': 60, 'hw': False, 'wm': True, 'video_mem': 268435456, 'blit_hw': False, 'blit_hw_CC': False, 'blit_hw_A': False, 'blit_sw': False, 'blit_sw_CC': False, 'blit_sw_A': False})>
Screen sizes: virtual=(1280, 720) physical=(1280, 720) drawable=(1280, 720)
Maximum texture size: 4096x4096
Could not open 'cache/shaders.txt':
Resetting cache.
Early init took 4 ms
Loading error handling took 5 ms
Loading script took 89 ms
Loading save slot metadata took 2 ms
Loading persistent took 0 ms

File "game/options.rpy", line 61: expected '=' not found.
    define config.keymap['game_menu'].append('mouseup_3')
                                     ^

File "game/options.rpy", line 62: expected '=' not found.
    define config.keymap['hide_windows'].append('mouseup_2')
                                        ^
DPI scale factor: 1.000000
nvdrs: Loaded, about to disable thread optimizations.
nvdrs: Disabled thread optimizations.
Interface start took 255 ms

Initializing gl2 renderer:
primary display bounds: (0, 0, 1920, 1080)
swap interval: 1 frames
Windowed mode.
Vendor: 'NVIDIA Corporation'
Renderer: 'NVIDIA GeForce RTX 3070 Laptop GPU/PCIe/SSE2'
Version: '4.6.0 NVIDIA 576.88'
Display Info: <Info({'bitsize': 32, 'bytesize': 4, 'masks': (16711680, 65280, 255, 0), 'shifts': (16, 8, 0, 0), 'losses': (0, 0, 0, 8), 'current_w': 1920, 'current_h': 1080, 'refresh_rate': 60, 'hw': False, 'wm': True, 'video_mem': 268435456, 'blit_hw': False, 'blit_hw_CC': False, 'blit_hw_A': False, 'blit_sw': False, 'blit_sw_CC': False, 'blit_sw_A': False})>
Screen sizes: virtual=(1280, 720) physical=(1280, 720) drawable=(1280, 720)
Maximum texture size: 4096x4096
Could not open 'cache/shaders.txt':
Resetting cache.
Early init took 4 ms
Loading error handling took 5 ms
Loading script took 180 ms
Loading save slot metadata took 10 ms
Loading persistent took 0 ms

Full traceback:
  File "game/options.rpy", line 6, in script
    define config.name_base = "Tenderness-Beyond-the-Wall"
  File "renpy/ast.py", line 2452, in execute
    self.set()
    ~~~~~~~~^^
  File "renpy/ast.py", line 2468, in set
    ns.set(self.varname, value)
    ~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "renpy/common/000namespaces.rpy", line 34, in set
    setattr(self.nso, name, value)
    ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "renpy/defaultstore.py", line 100, in __setattr__
    raise Exception("config.%s is not a known configuration variable." % (name))
Exception: config.name_base is not a known configuration variable.

While running game code:
  File "game/options.rpy", line 6, in script
    define config.name_base = "Tenderness-Beyond-the-Wall"
Exception: config.name_base is not a known configuration variable.
DPI scale factor: 1.000000
nvdrs: Loaded, about to disable thread optimizations.
nvdrs: Disabled thread optimizations.
Interface start took 302 ms

Initializing gl2 renderer:
primary display bounds: (0, 0, 1920, 1080)
swap interval: 1 frames
Windowed mode.
Vendor: 'NVIDIA Corporation'
Renderer: 'NVIDIA GeForce RTX 3070 Laptop GPU/PCIe/SSE2'
Version: '4.6.0 NVIDIA 576.88'
Display Info: <Info({'bitsize': 32, 'bytesize': 4, 'masks': (16711680, 65280, 255, 0), 'shifts': (16, 8, 0, 0), 'losses': (0, 0, 0, 8), 'current_w': 1920, 'current_h': 1080, 'refresh_rate': 60, 'hw': False, 'wm': True, 'video_mem': 268435456, 'blit_hw': False, 'blit_hw_CC': False, 'blit_hw_A': False, 'blit_sw': False, 'blit_sw_CC': False, 'blit_sw_A': False})>
Screen sizes: virtual=(1280, 720) physical=(1280, 720) drawable=(1280, 720)
Maximum texture size: 4096x4096
Could not open 'cache/shaders.txt':
Resetting cache.
Early init took 5 ms
Loading error handling took 5 ms
Loading script took 82 ms
Loading save slot metadata took 6 ms
Loading persistent took 0 ms

The label choice3_love is defined twice, at File "game/chapter3_extended.rpy", line 110:
label choice3_love:
and File "game/script.rpy", line 536:
label choice3_love:


The label choice3_happiness is defined twice, at File "game/chapter3_extended.rpy", line 160:
label choice3_happiness:
and File "game/script.rpy", line 586:
label choice3_happiness:


The label choice3_time is defined twice, at File "game/chapter3_extended.rpy", line 191:
label choice3_time:
and File "game/script.rpy", line 617:
label choice3_time:


The label chapter3_intimate is defined twice, at File "game/chapter3_extended.rpy", line 216:
label chapter3_intimate:
and File "game/script.rpy", line 642:
label chapter3_intimate:


The label chapter3_end is defined twice, at File "game/chapter3_extended.rpy", line 251:
label chapter3_end:
and File "game/script.rpy", line 677:
label chapter3_end:

DPI scale factor: 1.000000
nvdrs: Loaded, about to disable thread optimizations.
nvdrs: Disabled thread optimizations.

Full traceback:
  File "renpy/bootstrap.py", line 376, in bootstrap
    renpy.main.main()
    ~~~~~~~~~~~~~~~^^
  File "renpy/main.py", line 529, in main
    renpy.game.script.report_duplicate_labels()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "renpy/script.py", line 1222, in report_duplicate_labels
    if renpy.parser.report_parse_errors():
       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^ 
  File "renpy/parser.py", line 1794, in report_parse_errors
    renpy.display.error.report_parse_errors(screen_parse_errors, error_fn)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "renpy/display/error.py", line 181, in report_parse_errors
    init_display()
    ~~~~~~~~~~~~^^
  File "renpy/display/error.py", line 70, in init_display
    renpy.game.interface.start()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "renpy/display/core.py", line 1019, in start
    self.post_init()
    ~~~~~~~~~~~~~~^^
  File "renpy/display/core.py", line 1102, in post_init
    self.set_icon()
    ~~~~~~~~~~~~~^^
  File "renpy/display/core.py", line 1155, in set_icon
    with renpy.loader.load(icon, directory="images") as f:
         ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^      
  File "renpy/loader.py", line 698, in load
    raise FileNotFoundError("Couldn't find file '%s'." % name)
FileNotFoundError: Couldn't find file 'gui/window_icon.png'.

After initialization, but before game start.
FileNotFoundError: Couldn't find file 'gui/window_icon.png'.
